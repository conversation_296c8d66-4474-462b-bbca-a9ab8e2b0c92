import { useMemo, useState, useEffect } from 'react';
import * as models from 'api/models/boekestyns';
import { handleFocus } from '@/utils/focus';
import { formatNumber } from '@/utils/format';
//import { isBouquetMum } from '@/utils/bouquet-mums';

interface RecordLabourDialogItemProps {
  stickingOrder: models.StickingWorkOrderItem | null;
  variety: models.StickingWorkOrderVariety;
  history: models.StickingWorkOrderLabourVariety[];
  onChange: (
    varietyName: string,
    property: 'sticked' | 'thrownOut',
    value: number
  ) => void;
}

export function RecordLabourDialogItem({
  variety,
  stickingOrder,
  history,
  onChange,
}: RecordLabourDialogItemProps) {
  const [sticked, setSticked] = useState(''),
    [thrownOut, setThrownOut] = useState(''),
    previousStick = useMemo(
      () =>
        history
          .filter((h) => h.varietyId === variety.id)
          .reduce((total, h) => total + h.sticked + h.thrownOut, 0),
      [history, variety.name]
    ),
    remaining = useMemo(
      () =>
        Math.max(
          (stickingOrder?.varieties.find((v) => v.name === variety.name)
                ?.pots || 0) - previousStick,
          0
        ),
      [stickingOrder?.varieties, previousStick, variety.name]
    );

  useEffect(() => {
    setSticked('');
    setThrownOut('');
  }, [variety]);

  const handleStickedBlur = () => {
    const value = parseInt(sticked, 10);
    onChange(variety.name, 'sticked', isNaN(value) ? 0 : value);
  };

  const handleStickedChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSticked(e.target.value);
  };

  const handleThrownOutBlur = () => {
    const value = parseInt(thrownOut, 10);
    onChange(variety.name, 'thrownOut', isNaN(value) ? 0 : value);
  };

  const handleThrownOutChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setThrownOut(e.target.value);
  };

  return (
    <tr>
      <td className="w-1 whitespace-nowrap p-2 text-left">{variety.name}</td>
      <td className="w-1 p-2 text-right">
        {formatNumber(variety.pots, '0,0')}
      </td>
      <td className="w-1">{formatNumber(previousStick, '0,0')}</td>
      <td className="w-1 p-2 text-right">{formatNumber(remaining, '0,0')}</td>
      <td className="w-1 text-center">
        <input
          value={sticked}
          onChange={handleStickedChange}
          onBlur={handleStickedBlur}
          onFocus={handleFocus}
          className="block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td className="w-1 text-center">
        <input
          type="number"
          value={thrownOut}
          onChange={handleThrownOutChange}
          onBlur={handleThrownOutBlur}
          onFocus={handleFocus}
          className="block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td>&nbsp;</td>
    </tr>
  );
}
