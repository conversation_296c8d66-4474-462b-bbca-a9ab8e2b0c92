import { contains } from './equals';

export const isBouquetMum = (name: string) => {
  return (
    contains(name, 'Bouquet') ||
    contains(name, 'BQT') ||
    contains(name, 'Easter Egg')
  );
};

export const removeVarietiesForBouquetMums = (
  varieties: VarietyData[],
  name: string,
  workOrderId: number
) => {
  return isBouquetMum(name)
    ? [
        {
          id: -1,
          workOrderId,
          name: 'Bouquet',
          pots: varieties.reduce((sum, v) => sum + v.pots, 0),
          expectedHarvestPercentage: 100,
          comment: null,
          selected: true,
        },
      ]
    : varieties;
};

type VarietyData = {
  id: number;
  workOrderId: number;
  name: string;
  pots: number;
  expectedHarvestPercentage: number;
  comment: null;
  selected: boolean;
};
