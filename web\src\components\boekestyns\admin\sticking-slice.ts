import {
  createSlice,
  AsyncThunk,
  createAsyncThunk,
  createSelector,
} from '@reduxjs/toolkit';
import { boekestynStickingApi } from 'api/boekestyn-sticking-service';
import * as boeks from 'api/models/boekestyns';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { sortBy } from '@/utils/sort';

const sortByPlantName = sortBy('name');

export const ScheduleStickingOrderType = 'SCHEDULE_STICKING_ORDER';
export const SortStickingWorkOrderType = 'SORT_STICKING_WORK_ORDER';

export type SortFields = keyof boeks.StickingOrder | 'crop' | 'size' | 'stickingSortOrder';

export const getScheduleById: AsyncThunk<
  boeks.StickingOrder | undefined,
  string,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-sticking/getScheduleById',
  async (id, { getState, rejectWithValue }) => {
    try {
      const { boekestynStickingAdmin: boekestynSticking } =
          getState() as RootState,
        orders = boekestynSticking.orders,
        order = orders.find((o) => o._id === id);

      return order;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface MoveItemArgs {
  scheduleId: number;
  movingItem: boeks.StickingWorkOrder;
  existingItem: boeks.StickingWorkOrder;
}

export const moveItem: AsyncThunk<
  boeks.StickingWorkOrder[] | undefined,
  MoveItemArgs,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-sticking/moveItem',
  async (
    { scheduleId, movingItem, existingItem },
    { getState, rejectWithValue }
  ) => {
    try {
      const { boekestynStickingAdmin: boekestynSticking } =
          getState() as RootState,
        schedule = boekestynSticking.schedules.find((s) => s.id === scheduleId);

      if (schedule) {
        const copy = schedule.workOrders
            .filter((i) => i.id !== movingItem.id)
            .map((i) => ({ ...i })),
          index = copy.findIndex((i) => i.id === existingItem.id);

        if (movingItem) {
          copy.splice(index, 0, { ...movingItem });

          copy.forEach((i, index) => (i.sortOrder = index + 1));

          return copy;
        }
      }
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface StickingState {
  orders: boeks.StickingOrder[];
  lines: boeks.StickingLine[];
  schedules: boeks.StickingSchedule[];
  sort: SortFields;
  sortDescending?: boolean;
  plant: string | null;
}

const initialState: StickingState = {
  orders: [],
  lines: [],
  schedules: [],
  sort: 'stickingSortOrder',
  sortDescending: false,
  plant: null,
};

export interface SortArgs {
  sort: SortFields;
  sortDescending?: boolean;
}

const stickingSlice = createSlice({
  name: 'sticking',
  initialState,
  reducers: {
    setSort(state, { payload }: { payload: SortArgs }) {
      state.sort = payload.sort;
      state.sortDescending = payload.sortDescending;
    },
    setPlant(state, { payload }: { payload: string | null }) {
      state.plant = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addMatcher(
        boekestynStickingApi.endpoints.sticking.matchFulfilled,
        (state, { payload }) => {
          state.lines = payload.lines;
        }
      )
      .addMatcher(
        boekestynStickingApi.endpoints.stickingOrders.matchFulfilled,
        (state, { payload }) => {
          state.orders = payload.orders;
        }
      )
      .addMatcher(
        boekestynStickingApi.endpoints.stickingSchedules.matchFulfilled,
        (state, { payload }) => {
          state.schedules = payload.schedules;
        }
      ),
});

export const { setSort, setPlant } = stickingSlice.actions;

const selectAllOrders = ({
  boekestynStickingAdmin: boekestynSticking,
}: RootState) => boekestynSticking.orders;
export const selectLines = ({
  boekestynStickingAdmin: boekestynSticking,
}: RootState) => boekestynSticking.lines;
export const selectSchedules = ({
  boekestynStickingAdmin: boekestynSticking,
}: RootState) => boekestynSticking.schedules;
export const selectSort = ({
  boekestynStickingAdmin: boekestynSticking,
}: RootState) => boekestynSticking.sort;
export const selectSortDescending = ({
  boekestynStickingAdmin: boekestynSticking,
}: RootState) => boekestynSticking.sortDescending;
export const selectPlant = ({
  boekestynStickingAdmin: boekestynSticking,
}: RootState) => boekestynSticking.plant;

export const selectPlants = createSelector(selectAllOrders, (orders) => {
  const plants = orders
    .reduce((memo, o) => {
      if (!memo.some((p) => p._id === o.plant._id)) {
        memo.push(o.plant);
      }
      return memo;
    }, [] as boeks.StickingOrderPlant[])
    .sort(sortByPlantName);
  return [...new Set(plants)];
});

export const selectOrders = createSelector(
  selectAllOrders,
  selectSort,
  selectSortDescending,
  selectPlant,
  (orders, sort, sortDescending, plant) => {
    const sortFn = (a: boeks.StickingOrder, b: boeks.StickingOrder) => {
      if (a.stickingScheduled !== b.stickingScheduled) {
        if (a.stickingScheduled) {
          return 1;
        }
        return -1;
      }

      if (sort === 'stickingSortOrder') {
        return (
          (a.plant.stickingSortOrder || 0) - (b.plant.stickingSortOrder || 0)
        );
      } else if (sort === 'crop') {
        return (
          a.plant.crop.localeCompare(b.plant.crop) * (sortDescending ? -1 : 1)
        );
      } else if (sort === 'size') {
        return (
          a.plant.size.localeCompare(b.plant.size) * (sortDescending ? -1 : 1)
        );
      } else {
        return sortBy(sort, sortDescending ? 'descending' : '')(a, b);
      }
    };
    return orders.filter((o) => !plant || o.plant._id === plant).sort(sortFn);
  }
);

export default stickingSlice.reducer;
