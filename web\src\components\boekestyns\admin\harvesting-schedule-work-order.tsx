import { useRef, useState, useEffect, useMemo } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import {
  useSortHarvestingWorkOrdersMutation,
  useUpdateHarvestingWorkOrderCommentMutation,
  useHarvestingOrdersQuery,
  useLazySchedulesForOrderQuery,
} from 'api/boekestyn-harvesting-service';
import * as models from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { selectStartDate, selectEndDate } from './admin-slice';
import { moveItem, SortHarvestingWorkOrderType } from './harvesting-slice';
import { setSelectedOrder } from './harvesting-work-orders-slice';
import { HarvestingScheduleWorkOrderVariety } from './harvesting-schedule-work-order-variety';

interface HarvestingScheduleWorkOrderProps {
  scheduleId: number;
  order: models.HarvestingWorkOrder;
}

export function HarvestingScheduleWorkOrder({
  scheduleId,
  order,
}: HarvestingScheduleWorkOrderProps) {
  const dispatch = useAppDispatch(),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    [comment, setComment] = useState(order.harvestingComments ?? ''),
    [sortWorkOrders] = useSortHarvestingWorkOrdersMutation(),
    [updateWorkOrderComment] = useUpdateHarvestingWorkOrderCommentMutation(),
    [schedulesForOrder] = useLazySchedulesForOrderQuery(),
    ref = useRef<HTMLTableRowElement>(null),
    cellClassName = 'whitespace-nowrap px-2 py-1 text-gray-700 align-top',
    pots = useMemo(() => {
      if (order.labourVarieties.length) {
        return order.labourVarieties.reduce(
          (sum, lv) => sum + lv.harvested + lv.thrownOut,
          0
        );
      } else {
        return order.varieties.length
          ? order.varieties.reduce((sum, v) => sum + v.pots, 0)
          : order.pots;
      }
    }, [order]),
    [, drag] = useDrag(() => ({
      type: `${SortHarvestingWorkOrderType}-${scheduleId}`,
      item: order,
    })),
    [{ isOver }, drop] = useDrop<
      models.HarvestingWorkOrder,
      void,
      { isOver: boolean }
    >(() => ({
      accept: `${SortHarvestingWorkOrderType}-${scheduleId}`,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(droppedItem) {
        dispatch(
          moveItem({ scheduleId, existingItem: order, movingItem: droppedItem })
        ).then(({ payload }) => {
          const workOrders = payload as
            | models.HarvestingWorkOrder[]
            | undefined;
          if (workOrders) {
            const args = {
              workOrders: workOrders.map((wo) => ({
                workOrderId: wo.id,
                sortOrder: wo.sortOrder,
              })),
            };
            sortWorkOrders(args);
          }
        });
      },
    }));

  useHarvestingOrdersQuery({
    startDate,
    endDate,
  });

  useEffect(() => {
    setComment(order.harvestingComments ?? '');
  }, [order.harvestingComments]);

  const handleCommentsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setComment(event.target.value);
  };

  const handleCommentsBlur = async () => {
    if (comment !== order.harvestingComments) {
      const args = {
        id: order.id,
        comment,
      };
      await updateWorkOrderComment(args);
    }
  };

  const handleLotNumberClick = async () => {
    const response = await schedulesForOrder({ orderId: order.orderId });
    if (response.data?.order) {
      dispatch(setSelectedOrder(response.data.order));
    }
  };

  drag(drop(ref));

  return (
    <>
      <tr
        ref={ref}
        className={classNames(
          'border-gray-200',
          isOver && 'border-t-4 border-t-gray-500'
        )}
      >
        <td className={cellClassName}>
          <button
            type="button"
            className="btn-link"
            onClick={handleLotNumberClick}
          >
            {order.orderNumber}
          </button>
          {!!order.orderComments && (
            <div className="italic">{order.orderComments}</div>
          )}
        </td>
        <td className={cellClassName}>
          <span className="font-semibold">
            {order.plantSize}&nbsp;{order.plantCrop}&nbsp;{order.customer}
          </span>
          <div className="pl-4 italic">
            {order.varieties.map((variety) => (
              <HarvestingScheduleWorkOrderVariety
                key={variety.name}
                order={order}
                variety={variety}
              />
            ))}
          </div>
        </td>
        <td className={cellClassName}>
          <div className="flex flex-row">
            <div>
              <div>Crew Size: {order.crewSize}</div>
              {order.finalRound && <div className="italic">Final Round</div>}
            </div>
          </div>
        </td>
        <td className={classNames(cellClassName, 'text-right')}>
          {formatNumber(pots)} pots
        </td>
        <td className={classNames(cellClassName, 'w-1 text-center')}>
          <div className="flex flex-row">
            <div
              className="btn-secondary h-8 w-8 cursor-pointer px-2 py-1 text-center"
              // @ts-ignore
              ref={drag}
            >
              <Icon icon="arrows-up-down" />
            </div>
          </div>
        </td>
      </tr>
      <tr className="border-b border-gray-200">
        <td className={cellClassName} colSpan={6}>
          <label className="block text-xs italic">Schedule Notes</label>
          <input
            type="text"
            className="mb-2 w-full rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={comment ?? ''}
            onChange={handleCommentsChange}
            onBlur={handleCommentsBlur}
          />
        </td>
      </tr>
    </>
  );
}
