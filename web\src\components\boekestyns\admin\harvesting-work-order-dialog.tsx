import { useState } from 'react';
import moment from 'moment';
import * as HeadlessUI from '@headlessui/react';
import {
  CreateWorkOrdersArgs,
  useCreateWorkOrdersMutation,
} from 'api/boekestyn-harvesting-service';
import * as models from 'api/models/boekestyns';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { HarvestingWorkOrderDay } from './harvesting-work-order-day';
import {
  setSelectedOrder,
  addWorkOrderDate,
  removeWorkOrderDate,
  resetWorkOrders,
  selectSelectedOrder,
  selectSchedules,
  selectSelectedTab,
  setSelectedTab,
  setError,
  selectError,
} from './harvesting-work-orders-slice';
import { isBouquetMum } from '@/utils/bouquet-mums';

interface HarvestingWorkOrderDialogProps {
  line: models.HarvestingLine;
}

export function HarvestingWorkOrderDialog({
  line,
}: HarvestingWorkOrderDialogProps) {
  const dispatch = useAppDispatch(),
    selectedOrder = useAppSelector(selectSelectedOrder),
    workOrdersByDate = useAppSelector(selectSchedules),
    selectedTab = useAppSelector(selectSelectedTab),
    error = useAppSelector(selectError),
    [showDatePicker, setShowDatePicker] = useState(false),
    [workOrderDate, setWorkOrderDate] = useState(moment().format('YYYY-MM-DD')),
    [createWorkOrders] = useCreateWorkOrdersMutation();

  const handleSaveClick = async () => {
    if (selectedOrder) {
      const scheduledByVariety: { [index: string]: number } = {};

      const args: CreateWorkOrdersArgs = {
        orderId: selectedOrder.orderId,
        schedules: workOrdersByDate.map((date, index) => ({
          id: date.id,
          lineId: line.id,
          date: date.date,
          workOrders: [
            {
              id: date.workOrder.id,
              crewSize: date.workOrder.crewSize,
              harvestingComments: date.workOrder.harvestingComments,
              order: selectedOrder,
              stickingWorkOrderId: selectedOrder.stickingWorkOrderId,
              varieties: date.workOrder.varieties
                .filter((v) => v.selected)
                .map((v) => {
                  const expectedHarvestPercentage =
                      index === workOrdersByDate.length - 1
                        ? 100
                        : v.expectedHarvestPercentage,
                    // if it's a bouquet mum, the name will be 'Bouquet', but nothing will match
                    pots = isBouquetMum(selectedOrder.orderNumber)
                      ? selectedOrder.varieties.reduce(
                          (sum, ov) => sum + ov.pots,
                          0
                        )
                      : selectedOrder.varieties.find((ov) => ov.name === v.name)
                          ?.pots || 0,
                    expected = Math.round(
                      pots * (expectedHarvestPercentage / 100)
                    ),
                    scheduled = scheduledByVariety[v.name] || 0,
                    beginningQuantity = Math.max(pots - scheduled, 0);

                  if (v.name in scheduledByVariety) {
                    scheduledByVariety[v.name] += expected;
                  } else {
                    scheduledByVariety[v.name] = expected;
                  }

                  return {
                    id: v.id,
                    workOrderId: date.workOrder.id,
                    name: v.name,
                    pots,
                    beginningQuantity,
                    expectedHarvestPercentage,
                    comment: null,
                  };
                }),
              defaultExpectedHarvestPercentage:
                date.workOrder.defaultExpectedHarvestPercentage,
              finalRound: index === workOrdersByDate.length - 1,
            },
          ],
        })),
      };

      const response = await createWorkOrders(args);

      if (!('error' in response)) {
        dispatch(resetWorkOrders(null));
      }
    }
  };

  const handleAddWorkOrderDate = () => {
    dispatch(addWorkOrderDate(workOrderDate));
    setShowDatePicker(false);
  };

  const handleRemoveWorkOrderDate = (id: number) => {
    dispatch(removeWorkOrderDate(id));
  };

  const handleDatePickerCancel = () => {
    setShowDatePicker(false);
  };

  const handleClearError = () => {
    dispatch(setError(null));
  };

  return (
    <>
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30 max-w-5xl"
        onClose={() => dispatch(setSelectedOrder(null))}
        open={!!selectedOrder}
      >
        <HeadlessUI.Dialog.Overlay className="fixed inset-0 bg-gray-500/75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in" />
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex max-h-full min-h-full justify-center p-0 text-center">
            <HeadlessUI.Dialog.Panel className="relative my-8 flex w-full max-w-5xl transform flex-col overflow-y-auto rounded-lg bg-white p-4 text-left shadow-xl transition-all">
              <HeadlessUI.Tab.Group
                selectedIndex={selectedTab}
                onChange={(index) => dispatch(setSelectedTab(index))}
              >
                <HeadlessUI.Tab.List
                  as="nav"
                  className="flex w-full flex-shrink-0 flex-row overflow-x-auto border-b"
                >
                  {workOrdersByDate.map((date) => (
                    <HeadlessUI.Tab key={date.id}>
                      {({ selected }) => (
                        <div className="flex items-center px-8">
                          <button
                            type="button"
                            className={classNames(
                              'whitespace-nowrap border-b-2 px-1 py-2 text-xl font-medium',
                              selected
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                            )}
                          >
                            {moment(date.date).format('ddd')}
                            <div className="text-xs italic">
                              {moment(date.date).format('MMM D')}
                            </div>
                          </button>
                          <button
                            type="button"
                            className="btn-secondary ml-2 border-none p-1 shadow-none"
                            onClick={() => handleRemoveWorkOrderDate(date.id)}
                          >
                            <Icon icon="x" className="text-red-500" />
                          </button>
                        </div>
                      )}
                    </HeadlessUI.Tab>
                  ))}
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={() => setShowDatePicker(true)}
                  >
                    <Icon icon="plus" />
                  </button>
                </HeadlessUI.Tab.List>
                <HeadlessUI.Tab.Panels
                  as="div"
                  className="flex flex-grow flex-col overflow-y-auto border-t"
                >
                  {workOrdersByDate.map((date) => (
                    <HeadlessUI.Tab.Panel
                      key={date.id}
                      className="mt-4 flex flex-col overflow-y-auto"
                    >
                      <HarvestingWorkOrderDay schedule={date} />
                    </HeadlessUI.Tab.Panel>
                  ))}
                </HeadlessUI.Tab.Panels>
              </HeadlessUI.Tab.Group>
              <Error error={error} clear={handleClearError} />
              <div className="flex justify-end">
                <button
                  type="button"
                  className="btn-secondary ml-2"
                  onClick={() => dispatch(setSelectedOrder(null))}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn-primary ml-2"
                  onClick={handleSaveClick}
                >
                  Save
                </button>
              </div>
            </HeadlessUI.Dialog.Panel>
          </div>
          <HeadlessUI.Dialog
            as="div"
            className="relative z-40"
            open={showDatePicker}
            onClose={() => setShowDatePicker(false)}
          >
            <HeadlessUI.Dialog.Overlay className="fixed inset-0 bg-gray-500/75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in" />
            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <HeadlessUI.Dialog.Panel className="relative my-8 transform overflow-hidden rounded-lg bg-white p-4 text-left shadow-xl transition-all">
                  <div className="flex flex-col gap-4">
                    <div>
                      <label
                        htmlFor="new-work-order-date"
                        className="block text-sm font-medium text-gray-500"
                      >
                        Add Date
                      </label>
                      <input
                        type="date"
                        id="new-work-order-date"
                        name="new-work-order-date"
                        value={workOrderDate}
                        autoFocus
                        onChange={(e) => setWorkOrderDate(e.target.value)}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <div className="flex gap-2">
                      <button
                        type="button"
                        className="btn-secondary"
                        onClick={handleDatePickerCancel}
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="btn-primary"
                        onClick={handleAddWorkOrderDate}
                      >
                        Add
                      </button>
                    </div>
                  </div>
                </HeadlessUI.Dialog.Panel>
              </div>
            </div>
          </HeadlessUI.Dialog>
        </div>
      </HeadlessUI.Dialog>
    </>
  );
}
