import { useState, useCallback, useEffect, useMemo } from 'react';
import { DateTime, Duration } from 'luxon';
import { usePlantListQuery } from 'api/boekestyn-service';
import {
  useStartStickingWorkOrderLabourMutation,
  usePauseStickingWorkOrderLabourMutation,
  useStopStickingWorkOrderLabourMutation,
  StickingLabourVariety
} from 'api/boekestyn-sticking-service';
import * as boeks from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { useAppSelector } from '@/services/hooks';
import { formatNumber } from '@/utils/format';
import { selectWorkOrders, selectDate } from './sticking-slice';
//import { PauseLabourDialog } from './pause-labour-dialog';
import { RecordLabourDialog } from './record-labour-dialog';
import { StartLabourDialog } from './start-labour-dialog';

interface TimingProps {
  workOrder: boeks.StickingWorkOrderItem;
}

export function Timing({ workOrder }: TimingProps) {
  const labour = workOrder.labour,
    workOrders = useAppSelector(selectWorkOrders),
    date = useAppSelector(selectDate),
    inProcess = useMemo(() => labour.find((l) => !l.endTime) || null, [labour]),
    isFinalLabour = useMemo(() => labour.some((l) => l.finalLabour), [labour]),
    { data: plants } = usePlantListQuery(),
    [startLabour] = useStartStickingWorkOrderLabourMutation(),
    [pauseLabour] = usePauseStickingWorkOrderLabourMutation(),
    [stopLabour] = useStopStickingWorkOrderLabourMutation(),
    [ellapsed, setEllapsed] = useState(''),
    [timer, setTimer] = useState(0),
    [showStartDialog, setShowStartDialog] = useState(false),
    [showRecordDialog, setShowRecordDialog] = useState(false),
    isFirstAvailableLabour = useMemo(() => {
      const index = workOrders.findIndex((o) => o.id === workOrder.id),
        firstEligibleLabour = workOrders.find((o, i) => {
          return (
            i < index &&
            (!o.labour.length || o.labour.every((l) => !l.finalLabour))
          );
        });
      return !firstEligibleLabour;
    }, [workOrder.id, workOrders]),
    previousDaysLabour = useMemo(() => {
      if (labour.length) {
        const ellapsedMillis = labour.reduce((memo, l) => {
            if (!l.startTime) {
              return memo;
            }

            const now = DateTime.fromFormat(date, 'yyyy-MM-dd'),
              start = DateTime.fromFormat(l.startTime, 'yyyy-MM-dd HH:mm:ss'),
              isPrevious = start.diff(now, 'milliseconds').milliseconds < 0,
              started = start.toMillis(),
              ended = l.endTime
                ? DateTime.fromFormat(
                    l.endTime,
                    'yyyy-MM-dd HH:mm:ss'
                  ).toMillis()
                : Date.now().valueOf(),
              diff = ended - started;

            if (isPrevious) {
              return memo + diff;
            } else {
              return memo;
            }
          }, 0),
          duration = Duration.fromMillis(ellapsedMillis),
          ellapsed = ellapsedMillis
            ? ellapsedMillis < 60000
              ? `${duration.toFormat('s')} seconds`
              : `${duration.toFormat('m')} minutes`
            : '';

        return ellapsed;
      } else {
        return '';
      }
    }, [date, labour]),
    expectedPots = useMemo(() => {
      const plant = plants?.find(
        (p) => p.crop === workOrder.plantCrop && p.size === workOrder.plantSize
      );
      if (plant && labour.length) {
        const ellapsedMillis = labour.reduce((memo, l) => {
            if (!l.startTime) {
              return memo;
            }

            const start = DateTime.fromFormat(
                l.startTime,
                'yyyy-MM-dd HH:mm:ss'
              ),
              started = start.toMillis(),
              ended = l.endTime
                ? DateTime.fromFormat(
                    l.endTime,
                    'yyyy-MM-dd HH:mm:ss'
                  ).toMillis()
                : Date.now().valueOf(),
              diff = ended - started;

            return memo + diff;
          }, 0),
          todaysHours = ellapsedMillis / 3600000,
          expectedPots = Math.round(
            todaysHours * (plant.stickingCuttingsPerHour ?? 1)
          ),
          expectedTables = expectedPots / (plant.cuttingsPerTableTight || 1);

        return formatNumber(expectedTables, '0.0') + ' tables';
      }

      return '';
    }, [labour, plants, workOrder.plantCrop, workOrder.plantSize]);

  const update = useCallback(() => {
    if (labour.length) {
      const ellapsedMillis = labour.reduce((memo, l) => {
          if (!l.startTime) {
            return memo;
          }

          const now = DateTime.fromFormat(date, 'yyyy-MM-dd'),
            start = DateTime.fromFormat(l.startTime, 'yyyy-MM-dd HH:mm:ss'),
            isToday =
              start.hasSame(now, 'day') &&
              start.hasSame(now, 'month') &&
              start.hasSame(now, 'year'),
            started = start.toMillis(),
            ended = l.endTime
              ? DateTime.fromFormat(l.endTime, 'yyyy-MM-dd HH:mm:ss').toMillis()
              : Date.now().valueOf(),
            diff = ended - started;

          if (isToday) {
            return memo + diff;
          } else {
            return memo;
          }
        }, 0),
        todaysMillis = ellapsedMillis,
        duration = Duration.fromMillis(todaysMillis),
        ellapsed = todaysMillis
          ? todaysMillis < 60000
            ? `${duration.toFormat('s')} seconds`
            : `${duration.toFormat('m')} minutes`
          : '';

      setEllapsed(ellapsed);
    } else {
      setEllapsed('');
    }
  }, [date, labour]);

  useEffect(() => {
    if (timer) {
      window.clearInterval(timer);
    }

    const newTimer = window.setInterval(update, 1000);
    setTimer(newTimer);

    return () => {
      window.clearInterval(newTimer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [update]);

  const handleStartClick = () => {
    startLabour({
      workOrderId: workOrder.id,
      crewSize: workOrder.crewSize,
    });
  };

  const handleStopClick = async ({
      comments,
      crewSize,
      varieties,
      stickingComplete
    }: {
      comments: string | null;
      crewSize: number;
      varieties: StickingLabourVariety[];
      stickingComplete: boolean;
    }) => {
      if (inProcess) {
        if (timer) {
          window.clearInterval(timer);
        }
  
        stopLabour({
          workOrderId: workOrder.id,
          crewSize,
          comments,
          varietyQuantities: varieties,
          stickingComplete,
        });
      }
    };
  
    const openPauseDialog = async () => {
      if (inProcess) {
        if (timer) {
          window.clearInterval(timer);
        }
        setShowRecordDialog(true);
      }
    };
  
    const handlePauseClick = async ({
      comments,
    }: {
      comments: string | null;
    }) => {
      if (inProcess) {
        pauseLabour({
          workOrderId: workOrder.id,
          crewSize: inProcess.crewSize,
          comments,
        });
      }
  
      setShowRecordDialog(false);
    };

  return (
    <>
      {isFirstAvailableLabour && !inProcess && !isFinalLabour && (
        <button
          type="button"
          className="btn-secondary"
          onClick={handleStartClick}
        >
          <Icon icon="play" />
        </button>
      )}
      {isFirstAvailableLabour && !!inProcess && (
        <>
          <button
            type="button"
            className="btn-secondary"
            onClick={openPauseDialog}
          >
            <Icon icon="calculator" />
          </button>
          <button
            type="button"
            className="btn-secondary ml-2"
            onClick={() => handlePauseClick({ comments: '' })}
          >
            <Icon icon="pause" />
          </button>
        </>
      )}
      {!!previousDaysLabour && (
        <div className="text-sm font-semibold italic text-zinc-500">
          Yesterday: {previousDaysLabour}
        </div>
      )}
      {!!ellapsed && <p>{ellapsed}</p>}
      {!!expectedPots && (
        <div className="text-sm font-semibold italic text-zinc-500">
          Expected: {expectedPots}
        </div>
      )}
      {!!inProcess && <div className="italic text-zinc-500">Running</div>}
      {!inProcess && !!ellapsed && !isFinalLabour && (
        <div className="italic text-zinc-500">Paused</div>
      )}
      {isFinalLabour && <div className="font-light italic">Finished</div>}

      <RecordLabourDialog
        open={showRecordDialog}
        workOrder={workOrder}
        onClose={() => setShowRecordDialog(false)}
        onRecord={handleStopClick}
        inProcess={inProcess}
      />
      <StartLabourDialog
        open={showStartDialog}
        onClose={() => setShowStartDialog(false)}
        onStart={({crewSize}) => {
          setShowStartDialog(false);
          startLabour({
            workOrderId: workOrder.id,
            crewSize
          });
        }}
        order={workOrder}
      />
    </>
  );
}
