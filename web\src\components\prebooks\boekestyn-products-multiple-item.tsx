import { useState, useMemo, useEffect } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { useInventoryItemsQuery, useVendorsQuery } from 'api/spire-service';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { handleFocus } from '@/utils/focus';
import {
  selectBoekestynProducts,
  setBoekestynProducts,
  selectPlants,
  selectCustomers,
  BoekestynProduct,
} from './boekestyn-product-slice';
import * as boeks from 'api/models/boekestyns';
import * as settings from 'api/models/settings';
import * as spire from 'api/models/spire';
import { classNames } from '@/utils/class-names';
import { contains } from '@/utils/equals';

interface BoekestynProductsMultipleItemProps {
  product: BoekestynProduct;
  productDefault: settings.ProductDefault | undefined;
}

export function BoekestynProductsMultipleItem({
  product,
  productDefault,
}: BoekestynProductsMultipleItemProps) {
  const dispatch = useAppDispatch(),
    { data: inventoryItemsData } = useInventoryItemsQuery(),
    { data: vendorsData } = useVendorsQuery(),
    plants = useAppSelector(selectPlants),
    products = useAppSelector(selectBoekestynProducts),
    customers = useAppSelector(selectCustomers),
    [inventoryQuery, setInventoryQuery] = useState(''),
    [vendorQuery, setVendorQuery] = useState(''),
    inventoryItems = useMemo(
      () => inventoryItemsData?.inventoryItems || [],
      [inventoryItemsData]
    ),
    filteredInventoryItems = useMemo(
      () =>
        inventoryQuery.length < 3
          ? []
          : inventoryItems.filter(
              (item) =>
                contains(item.partNo, inventoryQuery) ||
                contains(item.description, inventoryQuery)
            ),
      [inventoryItems, inventoryQuery]
    ),
    vendors = useMemo(() => vendorsData || [], [vendorsData]),
    filteredVendors = useMemo(
      () =>
        vendorQuery.length < 3
          ? []
          : vendors.filter(
              (vendor) =>
                contains(vendor.name, vendorQuery) ||
                contains(vendor.vendorNo, vendorQuery)
            ),
      [vendors, vendorQuery]
    ),
    [componentType, setComponentType] = useState(
      product.boekestynPlantId ? 'boekestyn' : 'spire'
    ),
    [selectedInventoryItem, setSelectedInventoryItem] =
      useState<spire.InventoryItem | null>(),
    [selectedVendor, setSelectedVendor] = useState<spire.Vendor | null>();

  useEffect(() => {
    if (inventoryItemsData) {
      setSelectedInventoryItem(
        inventoryItemsData.inventoryItems.find(
          (item) => item.id === product.spireInventoryId
        ) || null
      );
    }
  }, [inventoryItemsData, product.spireInventoryId]);

  useEffect(() => {
    if (vendorsData) {
      setSelectedVendor(
        vendorsData.find((vendor) => vendor.id === product.vendorId) || null
      );
    }
  }, [vendorsData, product.vendorId]);

  const removeMapping = () => {
    dispatch(setBoekestynProducts(products.filter((m) => m.id !== product.id)));
  };

  const handleComponentTypeChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const newType = e.target.value;
    setComponentType(newType);

    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping) {
      if (newType === 'boekestyn') {
        // Clear Spire fields, keep Boekestyn fields
        mapping.spireInventoryId = null;
        mapping.spirePartNumber = null;
        mapping.vendorId = null;
        mapping.vendorName = null;
      } else if (newType === 'spire') {
        // Clear Boekestyn fields, keep Spire fields
        mapping.boekestynPlantId = '';
        mapping.boekestynCustomerAbbreviation = null;
      } else {
        // Clear all fields
        mapping.boekestynPlantId = '';
        mapping.boekestynCustomerAbbreviation = null;
        mapping.spireInventoryId = null;
        mapping.spirePartNumber = null;
        mapping.vendorId = null;
        mapping.vendorName = null;
      }
    }

    dispatch(setBoekestynProducts(updated));
  };

  const handlePlantIdChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping) {
      mapping.boekestynPlantId = e.target.value;
      // When selecting a Boekestyn plant, set vendor to Boekestyn
      if (e.target.value) {
        mapping.vendorId = boeks.BoekestynVendorId;
        mapping.vendorName = boeks.BoekestynVendorName;
      }
    }

    dispatch(setBoekestynProducts(updated));
  };

  const handleCustomerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping) {
      mapping.boekestynCustomerAbbreviation = e.target.value;
    }

    dispatch(setBoekestynProducts(updated));
  };

  const handleInventoryItemChange = (item: spire.InventoryItem | null) => {
    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping && item) {
      mapping.spireInventoryId = item.id;
      mapping.spirePartNumber = item.partNo;
      // Set vendor if available
      if (item.primaryVendor) {
        const vendor = vendors.find(
          (v) => v.vendorNo === item.primaryVendor?.vendorNo
        );
        if (vendor) {
          mapping.vendorId = vendor.id;
          mapping.vendorName = vendor.name;
        } else {
          mapping.vendorId = boeks.BoekestynVendorId;
          mapping.vendorName = boeks.BoekestynVendorName;
        }
      }
    } else if (mapping) {
      mapping.spireInventoryId = null;
      mapping.spirePartNumber = null;
      mapping.vendorId = null;
      mapping.vendorName = null;
    }

    setSelectedInventoryItem(item);
    dispatch(setBoekestynProducts(updated));
  };

  const handleVendorChange = (vendor: spire.Vendor | null) => {
    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping) {
      if (vendor) {
        mapping.vendorId = vendor.id;
        mapping.vendorName = vendor.name;
      } else {
        mapping.vendorId = null;
        mapping.vendorName = null;
      }
    }

    setSelectedVendor(vendor);
    dispatch(setBoekestynProducts(updated));
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping) {
      mapping.quantityPerFinishedItem = e.target.valueAsNumber || 0;
    }

    dispatch(setBoekestynProducts(updated));
  };

  const handleContributesToPackQuantityChange = (contributes: boolean) => {
    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping) {
      mapping.contributesToPackQuantity = contributes;
    }

    dispatch(setBoekestynProducts(updated));
  };

  return (
    <tr className="">
      {/* Component Type Column */}
      <td className="w-1 p-2">
        <select
          value={componentType}
          onChange={handleComponentTypeChange}
          className="block min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="boekestyn">Boekestyn Plant</option>
          <option value="spire">Spire Inventory</option>
        </select>
      </td>

      {/* Item/Plant Column */}
      <td className="p-2">
        {componentType === 'boekestyn' && (
          <select
            value={product.boekestynPlantId || ''}
            onChange={handlePlantIdChange}
            className="block w-full min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">Select Boekestyn Plant</option>
            {plants.map((plant) => (
              <option key={plant._id} value={plant._id}>
                {plant.name}
              </option>
            ))}
          </select>
        )}
        {componentType === 'spire' && (
          <HeadlessUI.Combobox
            as="div"
            value={selectedInventoryItem}
            onChange={handleInventoryItemChange}
            by="partNo"
          >
            <div className="relative mt-1">
              <HeadlessUI.Combobox.Input
                type="search"
                className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm"
                displayValue={(item: spire.InventoryItem | null) =>
                  item ? `${item.partNo} (${item.description})` : ''
                }
                placeholder="Search inventory..."
                onChange={(e) => setInventoryQuery(e.target.value)}
                autoComplete="off"
              />
              <HeadlessUI.Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                <Icon
                  icon="caret-down"
                  className="h-5 w-5 text-gray-400"
                  aria-hidden="true"
                />
              </HeadlessUI.Combobox.Button>

              <HeadlessUI.Combobox.Options className="fixed z-10 mt-1 max-h-60 w-full max-w-md overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                {filteredInventoryItems.length > 0 &&
                  filteredInventoryItems.map((item) => (
                    <HeadlessUI.Combobox.Option
                      key={item.id}
                      value={item}
                      className="cursor-pointer px-2 py-1 hover:bg-blue-100"
                    >
                      {item.partNo}
                      <span className="text-gray-500">
                        &nbsp;({item.description})
                      </span>
                    </HeadlessUI.Combobox.Option>
                  ))}
              </HeadlessUI.Combobox.Options>
            </div>
          </HeadlessUI.Combobox>
        )}
        {!componentType && (
          <span className="text-xs text-gray-400">
            Select component type first
          </span>
        )}
      </td>

      {/* Customer/Vendor Column */}
      <td className="p-2">
        {componentType === 'boekestyn' && (
          <select
            value={product.boekestynCustomerAbbreviation || ''}
            onChange={handleCustomerChange}
            className="block w-full min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">No Customer</option>
            {customers.map((customer) => (
              <option key={customer.abbreviation} value={customer.abbreviation}>
                {customer.name} ({customer.abbreviation})
              </option>
            ))}
          </select>
        )}
        {componentType === 'spire' && (
          <HeadlessUI.Combobox
            as="div"
            value={selectedVendor}
            onChange={handleVendorChange}
            by="partNo"
          >
            <div className="relative mt-1">
              <HeadlessUI.Combobox.Input
                type="search"
                className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm"
                displayValue={(vendor: spire.Vendor | null) =>
                  vendor?.name || ''
                }
                placeholder="Search vendors..."
                onChange={(e) => setVendorQuery(e.target.value)}
                autoComplete="off"
              />
              <HeadlessUI.Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                <Icon
                  icon="caret-down"
                  className="h-5 w-5 text-gray-400"
                  aria-hidden="true"
                />
              </HeadlessUI.Combobox.Button>

              <HeadlessUI.Combobox.Options className="fixed z-10 mt-1 max-h-60 w-full max-w-sm overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                {filteredVendors.length > 0 &&
                  filteredVendors.map((vendor) => (
                    <HeadlessUI.Combobox.Option
                      key={vendor.id}
                      value={vendor}
                      className="cursor-pointer px-2 py-1 hover:bg-blue-100"
                    >
                      {vendor.name}
                      <span className="text-gray-500">
                        &nbsp;{vendor.vendorNo}
                      </span>
                    </HeadlessUI.Combobox.Option>
                  ))}
              </HeadlessUI.Combobox.Options>
            </div>
          </HeadlessUI.Combobox>
        )}
      </td>

      {/* Quantity Column */}
      <td className="p-2 text-center">
        <input
          type="number"
          value={product.quantityPerFinishedItem}
          onChange={handleQuantityChange}
          onFocus={handleFocus}
          className="mx-auto block w-16 min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>

      {!productDefault?.ignoreOverrideQuantity && (
        <td className="p-2 text-center">
          <HeadlessUI.Switch.Group as="div">
            <HeadlessUI.Switch
              checked={product.contributesToPackQuantity}
              onChange={handleContributesToPackQuantityChange}
              className={classNames(
                product.contributesToPackQuantity
                  ? 'bg-blue-400 outline-none ring-2 ring-blue-500 ring-offset-2'
                  : 'bg-gray-200',
                'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out'
              )}
            >
              <span
                aria-hidden="true"
                className={classNames(
                  product.contributesToPackQuantity
                    ? 'translate-x-4'
                    : 'translate-x-0',
                  'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                )}
              />
            </HeadlessUI.Switch>
          </HeadlessUI.Switch.Group>
        </td>
      )}

      {/* Delete Column */}
      <td className="p-2">
        <button
          type="button"
          onClick={removeMapping}
          className="btn-delete px-2 py-1"
        >
          <Icon icon="trash" />
        </button>
      </td>
    </tr>
  );
}
