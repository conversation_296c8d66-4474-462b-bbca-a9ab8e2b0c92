import { useMemo } from 'react';
import moment from 'moment';
import * as boeks from 'api/models/boekestyns';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { sortBy } from '@/utils/sort';
import { Timing } from './timing';

const sortByStickingSortOrder = sortBy('stickingSortOrder');

interface StickingItemProps {
  workOrder: boeks.StickingWorkOrderItem;
}

export function StickingItem({ workOrder }: StickingItemProps) {
  const labour = workOrder.labour,
    inProcess = useMemo(() => labour.find((l) => !l.endTime) || null, [labour]),
    isFinalLabour = useMemo(() => labour.some((l) => l.finalLabour), [labour]),
    varieties = useMemo(
      () =>
        workOrder.varieties
          .map((v) => ({ ...v }))
          .sort(sortByStickingSortOrder),
      [workOrder.varieties]
    );

  return (
    <tr
      className={classNames(
        isFinalLabour ? 'bg-green-600' : '',
        workOrder.scheduleDate ? 'bg-yellow-100' : ''
      )}
    >
      <td
        className={classNames(
          'p-2 text-left align-top text-gray-900',
          inProcess ? 'border-b-4 border-l-4 border-t-4 border-blue-600' : ''
        )}
      >
        {!!workOrder.scheduleDate && (
          <div className="text-base font-semibold italic text-yellow-600">
            Scheduled for
            <br />
            {moment(workOrder.scheduleDate).format('dddd, MMM D')}
          </div>
        )}
        <Timing workOrder={workOrder} />
      </td>
      <td
        className={classNames(
          'p-2 text-left align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        <span className="font-semibold">{workOrder.orderNumber}</span>
        {!!workOrder.orderComments && (
          <div className="ml-2 italic">{workOrder.orderComments}</div>
        )}
        {!!workOrder.stickingComments && (
          <div className="ml-2 italic">{workOrder.stickingComments}</div>
        )}
      </td>
      <td
        className={classNames(
          'px-8 py-2 text-left align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        <span className="font-semibold">
          {workOrder.plantSize}&nbsp;{workOrder.plantCrop}&nbsp;
          {workOrder.customer}
        </span>

        {!isFinalLabour && !!varieties.length && (
          <div className="ml-2 italic">
            {varieties.map((variety) => (
              <div key={variety.name}>
                {variety.name}: {formatNumber(variety.cuttings)}
              </div>
            ))}
          </div>
        )}
      </td>
      <td
        className={classNames(
          'px-8 py-2 text-right align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        {formatNumber(workOrder.cuttings)}
      </td>
      <td
        className={classNames(
          'px-8 py-2 text-right align-top text-gray-900',
          inProcess ? 'border-b-4 border-r-4 border-t-4 border-blue-600' : ''
        )}
      >
        {workOrder.estimatedHours}
      </td>
    </tr>
  );
}
