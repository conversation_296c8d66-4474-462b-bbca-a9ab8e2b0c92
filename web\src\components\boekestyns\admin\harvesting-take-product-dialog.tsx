import { useState, Fragment, useMemo } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { Icon } from '@/components/icon';
import { useGetAllStickingWorkOrdersQuery, useGetStickingWorkOrdersByDateQuery } from 'api/boekestyn-sticking-service';
import { handleFocus } from '@/utils/focus';
import { TakeProductArgs, VarietyQuantity } from 'api/boekestyn-harvesting-service';
import { formatDate } from '@/utils/format';

// Extended interface to support multiple varieties


interface HarvestingTakeProductDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (args: TakeProductArgs) => void;
}

export function HarvestingTakeProductDialog({
  open,
  onClose,
  onSave,
}: HarvestingTakeProductDialogProps) {
  const [lotNumber, setLotNumber] = useState('');
  const [lotQuery, setLotQuery] = useState('');
  const [varieties, setVarieties] = useState<VarietyQuantity[]>([]);

  // Get today's date for sticking work orders
  const {data: stickingOrdersData} = useGetAllStickingWorkOrdersQuery();
  const allLotNumbers = stickingOrdersData?.orders.map((o) => o.orderNumber) ?? [];
  /*const allLotNumbers = useMemo(() =>
    stickingWorkOrdersData?.orders.map((o) => o.orderNumber) || [],
  [stickingWorkOrdersData?.orders]);*/

  // Filter lot numbers based on search query
  const filteredLotNumbers = lotQuery
    ? allLotNumbers?.filter((lot) =>
        lot.toLowerCase().includes(lotQuery.toLowerCase())
      )
    : allLotNumbers;

  const handleTransitionAfterEnter = () => {
    setLotNumber('');
    setLotQuery('');
    setVarieties([]);
  };

  const handleLotNumberChange = (value: string) => {
    setLotNumber(value);
    setLotQuery(value);

    // Update varieties when lot number changes
    const selectedOrder = stickingOrdersData?.orders.find((o) => o.orderNumber === value);
    if (selectedOrder) {
      const newVarieties: VarietyQuantity[] = selectedOrder.varieties.map((variety) => ({
        varietyName: variety.name,
        quantity: 0,
        availableQuantity: variety.pots, // Using pots as available quantity
      }));
      setVarieties(newVarieties);
    } else {
      setVarieties([]);
    }
  };

  const handleVarietyQuantityChange = (varietyName: string, quantity: number) => {
    setVarieties(prev =>
      prev.map(v =>
        v.varietyName === varietyName
          ? { ...v, quantity: Math.max(0, Math.min(quantity, v.availableQuantity)) }
          : v
      )
    );
  };

  const handleSaveClick = () => {
    const varietiesWithQuantity = varieties.filter(v => v.quantity > 0);
    if (lotNumber && varietiesWithQuantity.length > 0) {
      onSave({ lotNumber, varieties: varietiesWithQuantity });
      onClose();
    }
  };

  const handleCancelClick = () => {
    onClose();
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterEnter={handleTransitionAfterEnter}
    >
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-lg transform overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all">
                <div>
                  <div className="flex items-center">
                    <div className="flex flex-col">
                      <HeadlessUI.Dialog.Title
                        as="h3"
                        className="text-lg font-medium leading-6 text-gray-900"
                      >
                        Take Product
                      </HeadlessUI.Dialog.Title>
                    </div>
                  </div>

                  <form className="mt-6 space-y-4">
                    <div>
                      <HeadlessUI.Combobox
                        value={lotNumber}
                        onChange={handleLotNumberChange}
                      >
                        <HeadlessUI.Combobox.Label className="block text-sm font-medium text-gray-700">
                          Lot Number
                        </HeadlessUI.Combobox.Label>
                        <div className="relative mt-1">
                          <HeadlessUI.Combobox.Input
                            type="search"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            placeholder="Search lot numbers..."
                            autoComplete="off"
                            onChange={(e) => setLotQuery(e.target.value)}
                            displayValue={(value: string) => value}
                          />
                          <HeadlessUI.Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                            <Icon
                              icon="caret-down"
                              className="h-5 w-5 text-gray-400"
                              aria-hidden="true"
                            />
                          </HeadlessUI.Combobox.Button>
                        </div>

                        {filteredLotNumbers.length > 0 && (
                          <HeadlessUI.Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                            {filteredLotNumbers.map((lot) => (
                              <HeadlessUI.Combobox.Option
                                key={lot}
                                value={lot}
                                className={({ active }) =>
                                  `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                    active
                                      ? 'bg-blue-600 text-white'
                                      : 'text-gray-900'
                                  }`
                                }
                              >
                                {({ active, selected }) => (
                                  <>
                                    <span
                                      className={`block truncate ${
                                        selected ? 'font-semibold' : 'font-normal'
                                      }`}
                                    >
                                      {lot}
                                    </span>
                                    {selected && (
                                      <span
                                        className={`absolute inset-y-0 right-0 flex items-center pr-4 ${
                                          active ? 'text-white' : 'text-blue-600'
                                        }`}
                                      >
                                        <Icon
                                          icon="check"
                                          className="h-5 w-5"
                                          aria-hidden="true"
                                        />
                                      </span>
                                    )}
                                  </>
                                )}
                              </HeadlessUI.Combobox.Option>
                            ))}
                          </HeadlessUI.Combobox.Options>
                        )}
                      </HeadlessUI.Combobox>
                    </div>

                    {lotNumber && varieties.length > 0 && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                          Varieties & Quantities
                        </label>
                        <div className="space-y-3 max-h-60 overflow-y-auto">
                          {varieties.map((variety) => (
                            <div key={variety.varietyName} className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                              <div className="flex-1">
                                <div className="font-medium text-gray-900">{variety.varietyName}</div>
                                <div className="text-sm text-gray-500">
                                  Available: {variety.availableQuantity} pots
                                </div>
                              </div>
                              <div className="ml-4">
                                <input
                                  type="number"
                                  min="0"
                                  max={variety.availableQuantity}
                                  step="1"
                                  className="w-20 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                  placeholder="0"
                                  defaultValue={0}
                                  value={variety.quantity || ''}
                                  onFocus={handleFocus}
                                  onChange={(e) => handleVarietyQuantityChange(variety.varietyName, e.target.valueAsNumber || 0)}
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Show message when no lot selected */}
                    {!lotNumber && (
                      <div className="text-center py-8 text-gray-500">
                        Select a lot number to view available varieties
                      </div>
                    )}

                    {/* Show message when lot selected but no varieties */}
                    {lotNumber && varieties.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        No varieties found for selected lot number
                      </div>
                    )}
                  </form>
                </div>

                {/* Action Buttons */}
                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={handleCancelClick}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary"
                    onClick={handleSaveClick}
                    disabled={!lotNumber || varieties.filter(v => v.quantity > 0).length === 0}
                  >
                    Save
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}