import { useState, Fragment, useEffect, useMemo } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as boeks from 'api/models/boekestyns';
import * as models from 'api/models/boekestyns';
import { Error } from '@/components/error';
import { classNames } from '@/utils/class-names';
import { ProblemDetails } from '@/utils/problem-details';
import { sortBy } from '@/utils/sort';
import {
  useLazySchedulesForOrderQuery,
  useStickingLabourQuery,
  StickingLabourVariety,
} from 'api/boekestyn-sticking-service';
import { RecordLabourDialogItem } from './record-labour-dialog-item';

const sortByStickingSortOrder = sortBy('stickingSortOrder');

interface PauseLabourDialogProps {
  workOrder: boeks.StickingWorkOrderItem;
  open: boolean;
  onClose: () => void;
  onRecord: (args: {
    comments: string | null;
    crewSize: number;
    varieties: StickingLabourVariety[];
    stickingComplete: boolean;
  }) => void;
  inProcess: boeks.StickingWorkOrderLabour | null;
}

export function RecordLabourDialog({
  workOrder,
  open,
  onClose,
  onRecord,
  inProcess,
}: PauseLabourDialogProps) {
  const [comments, setComments] = useState('');
  const [stickingComplete, setStickingComplete] = useState(false);
  const [crewSize, setCrewSize] = useState('1');
  const [stickingOrder, setStickingOrder] =
    useState<models.StickingWorkOrderItem | null>(null);
  const [labourVarieties, setLabourVarieties] = useState<
    StickingLabourVariety[]
  >([]);
  const [error, setError] = useState<ProblemDetails | null>(null);
  const { data: stickingLabour } = useStickingLabourQuery(workOrder.orderId, {
    skip: !workOrder.orderId,
  });
  const [query] = useLazySchedulesForOrderQuery();
  const history = useMemo(
      () =>
        stickingLabour?.labour.reduce((memo, l) => {
          l.varieties.forEach((v) => memo.push(v));
          return memo;
        }, [] as models.StickingWorkOrderLabourVariety[]) ?? [],
      [stickingLabour]
    ),
    varieties = useMemo(
      () =>
        workOrder.varieties
          .map((v) => ({ ...v }))
          .sort(sortByStickingSortOrder),
      [workOrder.varieties]
    );

  useEffect(() => {
    async function fetchStickingOrders() {
      setError(null);

      if (workOrder) {
        if (inProcess) {
          setCrewSize(inProcess.crewSize.toString());
        } else {
          setCrewSize(workOrder.crewSize.toString());
        }

        const { data } = await query({
          orderId: workOrder.orderId,
        });

        const matchingOrder = data?.order;
        if (matchingOrder) {
          setStickingOrder(matchingOrder);
          setLabourVarieties(
            matchingOrder.varieties.map((v) => ({
              varietyName: v.name,
              sticked: 0,
              thrownOut: 0,
            }))
          );
        }
      }
    }

    fetchStickingOrders();
  }, [query, workOrder, inProcess]);

  const handleClearError = () => {
    setError(null);
  };

  const handleTransitionAfterEnter = () => {
    setComments('');
  };

  const handleCrewSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCrewSize(e.target.value);
  };

  const handlePauseChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setComments(e.target.value);
  };

  const handleStickingCompleteChange = (value: boolean) => {
    setStickingComplete(value);
  };

  const handleSaveClick = () => {
    const crewSizeValue = isNaN(parseInt(crewSize, 10))
        ? 1
        : parseInt(crewSize, 10),
      varieties = labourVarieties
        .map((v) => ({ ...v }))
        .filter((v) => v.sticked > 0 || v.thrownOut > 0);

    onRecord({
      comments,
      crewSize: crewSizeValue,
      varieties,
      stickingComplete,
    });
    onClose();
  };

  const handleVarietyChange = (
    varietyName: string,
    property: 'sticked' | 'thrownOut',
    value: number
  ) => {
    const varieties = labourVarieties.map((v) => ({ ...v })),
      variety = varieties.find((v) => v.varietyName === varietyName);

    if (variety) {
      variety[property] = value;
    }
    setLabourVarieties(varieties);
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterEnter={handleTransitionAfterEnter}
    >
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-4xl transform overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all">
                <div>
                  <div className="mt-3 text-center">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      Record Sticking
                    </HeadlessUI.Dialog.Title>
                    <form className="mt-5">
                      <div className="mt-5">
                        <div className="text-left">
                          <label>Crew Size</label>
                          <input
                            type="number"
                            name="comments"
                            className="block w-40 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            value={crewSize}
                            onChange={handleCrewSizeChange}
                          />
                        </div>
                        <table className="min-w-full divide-y divide-gray-300 text-sm">
                          <thead>
                            <tr>
                              <th className="w-1 p-2 align-top">Variety</th>
                              <th className="w-1 p-2 text-right align-top">
                                Planted
                              </th>
                              <th className="w-1 p-2 text-right align-top">
                                Previous Sticking
                              </th>
                              <th className="w-1 p-2 text-right align-top">
                                Remaining
                              </th>
                              <th className="w-1 whitespace-nowrap p-2 text-center align-top">
                                Stuck
                              </th>
                              <th className="w-1 whitespace-nowrap p-2 text-center align-top">
                                Thrown Out
                              </th>
                              <th>&nbsp;</th>
                            </tr>
                          </thead>
                          <tbody>
                            {varieties.map((variety) => (
                              <RecordLabourDialogItem
                                key={variety.name}
                                stickingOrder={stickingOrder}
                                variety={variety}
                                history={history}
                                onChange={handleVarietyChange}
                              />
                            ))}
                          </tbody>
                        </table>
                      </div>

                      <div className="text-left">
                        <label>Comments</label>
                        <textarea
                          name="comments"
                          className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                          value={comments}
                          onChange={handlePauseChange}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">
                          Sticking Complete?
                        </label>
                        <div>
                          <HeadlessUI.Switch
                            checked={stickingComplete}
                            onChange={handleStickingCompleteChange}
                            className={classNames(
                              stickingComplete ? 'bg-blue-400' : 'bg-gray-200',
                              'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                            )}
                          >
                            <span
                              aria-hidden="true"
                              className={classNames(
                                stickingComplete
                                  ? 'translate-x-5'
                                  : 'translate-x-0',
                                'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                              )}
                            />
                          </HeadlessUI.Switch>
                        </div>
                      </div>
                    </form>
                  </div>
                  <Error error={error} clear={handleClearError} />
                </div>
                <div className="mt-6 text-right">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary ml-2"
                    onClick={handleSaveClick}
                  >
                    Record Sticking
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
