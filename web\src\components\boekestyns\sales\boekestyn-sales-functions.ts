import * as boeks from 'api/models/boekestyns';
import * as futureOrders from 'api/models/future-orders';
import * as prebooks from 'api/models/prebooks';
import * as settings from 'api/models/settings';
import { weekFromDate } from '@/utils/weeks';

export function boekCaseQuantity(
  plants: boeks.Plant[],
  prebookItem: boeks.BoekestynPrebookItem
) {
  const plant = plants.find((p) => p._id === prebookItem.boekestynPlantId),
    boekPackQuantity = plant?.potsPerCase || 1,
    pots = prebookItem.orderQuantity * prebookItem.packQuantity,
    quantityPerFinishedItem = prebookItem.quantityPerFinishedItem || 1,
    divisor =
      // if we're ignoring the override quantity, the qty / finished item is in cases. Otherwise it's in pots
      prebookItem.multipleProducts &&
      prebookItem.packQuantity &&
      !prebookItem.ignoreOverrideQuantity
        ? prebookItem.packQuantity
        : 1,
    boekCases = Math.ceil(
      ((pots / boekPackQuantity) * quantityPerFinishedItem) / divisor
    );

  return boekCases;
}

export function getBoekestynProducts(
  vendorId: number | null | undefined,
  requiredDate: string | null,
  season: prebooks.Season | null,
  productDefault: settings.ProductDefault | undefined
) {
  const boekestynProducts: futureOrders.FutureOrderBoekestynProduct[] = [];

  if (requiredDate) {
    const week = weekFromDate(requiredDate)?.week || 0,
      overrides =
        vendorId === boeks.BoekestynVendorId
          ? productDefault?.overrides
              .filter((o) => o.startWeek <= week && o.endWeek >= week)
              .map(
                ({
                  boekestynPlantId,
                  boekestynCustomerAbbreviation,
                  quantityPerFinishedItem,
                }) => ({
                  boekestynPlantId,
                  boekestynCustomerAbbreviation,
                  quantityPerFinishedItem,
                  spireInventoryId: null,
                  spirePartNumber: null,
                  vendorId: null,
                  vendorName: null,
                  contributesToPackQuantity:
                    !productDefault?.ignoreOverrideQuantity,
                })
              ) || []
          : [];

    boekestynProducts.push(...overrides);
  } else if (season) {
    const week = weekFromDate(season.seasonDate)?.week || 0,
      overrides =
        vendorId === boeks.BoekestynVendorId
          ? productDefault?.overrides
              .filter((o) => o.startWeek <= week && o.endWeek >= week)
              .map(
                ({
                  boekestynPlantId,
                  boekestynCustomerAbbreviation,
                  quantityPerFinishedItem,
                }) => ({
                  boekestynPlantId,
                  boekestynCustomerAbbreviation,
                  quantityPerFinishedItem,
                  spireInventoryId: null,
                  spirePartNumber: null,
                  vendorId: null,
                  vendorName: null,
                  contributesToPackQuantity:
                    !productDefault?.ignoreOverrideQuantity,
                })
              ) || []
          : [];

    boekestynProducts.push(...overrides);
  }

  if (vendorId === boeks.BoekestynVendorId && !boekestynProducts.length) {
    const products =
      productDefault?.products.map(
        ({
          boekestynPlantId,
          boekestynCustomerAbbreviation,
          quantityPerFinishedItem,
        }) => ({
          boekestynPlantId,
          boekestynCustomerAbbreviation,
          quantityPerFinishedItem,
          spireInventoryId: null,
          spirePartNumber: null,
          vendorId: null,
          vendorName: null,
          contributesToPackQuantity: !productDefault?.ignoreOverrideQuantity,
        })
      ) || [];

    boekestynProducts.push(...products);
  }
  return boekestynProducts;
}
