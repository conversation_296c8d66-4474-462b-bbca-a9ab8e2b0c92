import { useState, useMemo, useEffect } from 'react';
import * as boeks from 'api/models/boekestyns';
import * as settings from 'api/models/settings';
import * as spire from 'api/models/spire';
import { useInventoryItemsQuery, useVendorsQuery } from 'api/spire-service';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { contains } from '@/utils/equals';
import { handleFocus } from '@/utils/focus';
import {
  selectBoekestynProducts,
  setBoekestynProducts,
  selectPlants,
  selectCustomers,
  BoekestynProduct,
} from './boekestyn-product-slice';

interface BoekestynProductsMultipleItemProps {
  product: BoekestynProduct;
  productDefault: settings.ProductDefault | undefined;
}

export function BoekestynProductsMultipleItem({
  product,
  productDefault,
}: BoekestynProductsMultipleItemProps) {
  const dispatch = useAppDispatch(),
    { data: inventoryItemsData } = useInventoryItemsQuery(),
    { data: vendorsData } = useVendorsQuery(),
    plants = useAppSelector(selectPlants),
    products = useAppSelector(selectBoekestynProducts),
    customers = useAppSelector(selectCustomers),
    [inventoryQuery, setInventoryQuery] = useState(''),
    [vendorQuery, setVendorQuery] = useState(''),
    inventoryItems = useMemo(
      () => inventoryItemsData?.inventoryItems || [],
      [inventoryItemsData]
    ),
    filteredInventoryItems = useMemo(
      () =>
        inventoryQuery.length < 3
          ? []
          : inventoryItems.filter(
              (item) =>
                contains(item.partNo, inventoryQuery) ||
                contains(item.description, inventoryQuery)
            ),
      [inventoryItems, inventoryQuery]
    ),
    vendors = useMemo(() => vendorsData || [], [vendorsData]),
    filteredVendors = useMemo(
      () =>
        vendorQuery.length < 3
          ? []
          : vendors.filter(
              (vendor) =>
                contains(vendor.name, vendorQuery) ||
                contains(vendor.vendorNo, vendorQuery)
            ),
      [vendors, vendorQuery]
    ),
    [componentType, setComponentType] = useState(
      product.boekestynPlantId ? 'boekestyn' : 'spire'
    ),
    [selectedInventoryItem, setSelectedInventoryItem] =
      useState<spire.InventoryItem | null>(),
    [selectedVendor, setSelectedVendor] = useState<spire.Vendor | null>();

  const removeMapping = () => {
    dispatch(
      setBoekestynProducts(
        products.filter((m) => m.boekestynPlantId !== product.boekestynPlantId)
      )
    );
  };

  const handlePlantIdChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping) {
      mapping.boekestynPlantId = e.target.value;
    }

    dispatch(setBoekestynProducts(updated));
  };

  const handleCustomerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping) {
      mapping.boekestynCustomerAbbreviation = e.target.value;
    }

    dispatch(setBoekestynProducts(updated));
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const updated = products.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === product.id);

    if (mapping) {
      mapping.quantityPerFinishedItem = e.target.valueAsNumber || 0;
    }

    dispatch(setBoekestynProducts(updated));
  };

  return (
    <tr className="">
      <td className="p-2">
        <select
          value={product.boekestynPlantId || ''}
          onChange={handlePlantIdChange}
          className="block w-full min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="">No Boekestyn Plant</option>
          {plants.map((plant) => (
            <option key={plant._id} value={plant._id}>
              {plant.name}
            </option>
          ))}
        </select>
      </td>
      <td className="p-2">
        <select
          value={product.boekestynCustomerAbbreviation || ''}
          onChange={handleCustomerChange}
          className="block w-auto min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="">No Customer</option>
          {customers.map((customer) => (
            <option key={customer.abbreviation} value={customer.abbreviation}>
              {customer.name} ({customer.abbreviation})
            </option>
          ))}
        </select>
      </td>
      <td className="p-2 text-center">
        <input
          type="number"
          value={product.quantityPerFinishedItem}
          onChange={handleQuantityChange}
          onFocus={handleFocus}
          className="mx-auto block w-16 min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td className="p-2">
        <button
          type="button"
          onClick={removeMapping}
          className="btn-delete px-2 py-1"
        >
          <Icon icon="trash" />
        </button>
      </td>
    </tr>
  );
}
