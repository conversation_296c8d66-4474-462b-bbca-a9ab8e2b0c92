import { useMemo, useRef, useState } from 'react';
import { useDrop } from 'react-dnd';
import {
  useAddStickingOrderToStickingScheduleMutation,
  useStickingOrdersQuery,
} from 'api/boekestyn-sticking-service';
import * as models from 'api/models/boekestyns';
import { useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { selectStartDate, selectEndDate } from './admin-slice';
import { selectSchedules, ScheduleStickingOrderType } from './sticking-slice';
import { StickingScheduleWorkOrder } from './sticking-schedule-work-order';
import {
  StickingWorkOrderDialog,
  StickingWorkorderDialogVariety,
} from './sticking-work-order-dialog';

interface StickingScheduleProps {
  date: string;
  line: models.StickingLine;
}

export function StickingSchedule({ line, date }: StickingScheduleProps) {
  const [addOrderToSchedule] = useAddStickingOrderToStickingScheduleMutation(),
    [showDetailDialog, setShowDetailDialog] =
      useState<models.StickingOrder | null>(null),
    schedules = useAppSelector(selectSchedules),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    schedule = useMemo(
      () =>
        schedules.find((s) => s.lineId === line.id && s.date === date) ?? {
          id: 0,
          lineId: line.id,
          date,
          workOrders: [],
        },
      [schedules, line.id, date]
    ),
    estimatedHours = useMemo(
      () =>
        schedule.workOrders.reduce((total, o) => total + o.estimatedHours, 0) ??
        0,
      [schedule]
    ),
    manHours = useMemo(
      () =>
        schedule.workOrders.reduce(
          (total, o) => total + o.estimatedHours * o.crewSize,
          0
        ) ?? 0,
      [schedule]
    ),
    [{ isOver }, drop] = useDrop<
      models.StickingOrder,
      void,
      { isOver: boolean }
    >(() => ({
      accept: ScheduleStickingOrderType,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(order) {
        setShowDetailDialog(order);
      },
    })),
    ref = useRef<HTMLDivElement>(null);

  useStickingOrdersQuery({
    startDate,
    endDate,
  });

  const handleDialogClose = () => {
    setShowDetailDialog(null);
  };

  const handleDialogSave = (data: {
    crewSize?: number;
    flowerDate?: string;
    varietyQuantities?: StickingWorkorderDialogVariety[];
  }) => {
    if (data.crewSize && showDetailDialog) {
      const plant = {
          ...showDetailDialog.plant,
          defaultStickingCrewSize: data.crewSize,
        },
        varieties =
          data.varietyQuantities?.map((v) => ({
            name: v.name,
            cuttings: v.quantity * showDetailDialog.plant.cuttingsPerPot,
            pots: v.quantity,
            cases: Math.ceil(v.quantity / showDetailDialog.plant.potsPerCase),
            stickingSortOrder: v.stickingSortOrder,
            colour: v.colour,
          })) ?? [],
        order = {
          ...showDetailDialog,
          plant,
          flowerDate: data.flowerDate ?? '',
          varieties,
        };
      addOrderToSchedule({
        schedule,
        order,
      });
    }
    setShowDetailDialog(null);
  };

  drop(ref);
  return (
    <div
      ref={ref}
      className={classNames(
        'm-2 rounded border p-2',
        isOver && 'border-green-600'
      )}
    >
      <h2 className="text-lg font-semibold">{line.name}</h2>
      <div className="flex-grow">
        {!!schedule.id && (
          <table className="min-w-full divide-y divide-gray-300 text-sm">
            <thead>
              <tr className="sticky top-0 z-10">
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Lot #
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Size / Plant
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Pots
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Hours
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-center">
                  Crew Size
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Man Hours
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  &nbsp;
                </th>
              </tr>
            </thead>
            <tbody>
              {schedule.workOrders.map((order) => (
                <StickingScheduleWorkOrder
                  key={order.id}
                  order={order}
                  scheduleId={schedule.id}
                />
              ))}
            </tbody>
            <tfoot>
              <tr>
                <th colSpan={3} className="p-2 text-right">
                  Total Hours:
                </th>
                <th className="p-2 text-right">
                  {formatNumber(estimatedHours, '0,0.0')}
                </th>
                <th className="p-2 text-right">&nbsp;</th>
                <th className="p-2 text-right">
                  {formatNumber(manHours, '0,0.0')}
                </th>
                <th className="p-2 text-right">&nbsp;</th>
              </tr>
            </tfoot>
          </table>
        )}
        {!schedule.id && (
          <div className="flex h-24 items-center justify-center text-gray-500">
            <span className="text-sm italic">Drag to schedule orders.</span>
          </div>
        )}
      </div>
      <StickingWorkOrderDialog
        open={!!showDetailDialog}
        onClose={handleDialogClose}
        onSave={handleDialogSave}
        order={showDetailDialog}
      />
    </div>
  );
}
